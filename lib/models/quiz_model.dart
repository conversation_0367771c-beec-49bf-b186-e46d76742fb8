// نموذج الاختبار والأسئلة
class Quiz {
  final String title;
  final List<Question> questions;
  final DateTime createdAt;
  final QuizDifficulty difficulty;
  final QuizLanguage language;

  Quiz({
    required this.title,
    required this.questions,
    required this.createdAt,
    this.difficulty = QuizDifficulty.medium,
    this.language = QuizLanguage.arabic,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'questions': questions.map((q) => q.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'difficulty': difficulty.index,
      'language': language.index,
    };
  }

  factory Quiz.fromJson(Map<String, dynamic> json) {
    return Quiz(
      title: json['title'],
      questions: (json['questions'] as List)
          .map((q) => Question.fromJson(q))
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      difficulty: json['difficulty'] != null
          ? QuizDifficulty.values[json['difficulty']]
          : QuizDifficulty.medium,
      language: json['language'] != null
          ? QuizLanguage.values[json['language']]
          : QuizLanguage.arabic,
    );
  }
}

class Question {
  final String question;
  final List<String> options;
  final int correctAnswerIndex;
  
  Question({
    required this.question,
    required this.options,
    required this.correctAnswerIndex,
  });

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'correctAnswerIndex': correctAnswerIndex,
    };
  }

  factory Question.fromJson(Map<String, dynamic> json) {
    return Question(
      question: json['question'],
      options: List<String>.from(json['options']),
      correctAnswerIndex: json['correctAnswerIndex'],
    );
  }
}

// تعريف مستويات صعوبة الاختبار
enum QuizDifficulty {
  easy,
  medium,
  hard
}

extension QuizDifficultyExtension on QuizDifficulty {
  String get arabicName {
    switch (this) {
      case QuizDifficulty.easy:
        return 'سهل';
      case QuizDifficulty.medium:
        return 'متوسط';
      case QuizDifficulty.hard:
        return 'صعب';
    }
  }
}

// تعريف لغات الاختبار
enum QuizLanguage {
  arabic,
  english
}

extension QuizLanguageExtension on QuizLanguage {
  String get displayName {
    switch (this) {
      case QuizLanguage.arabic:
        return 'العربية';
      case QuizLanguage.english:
        return 'English';
    }
  }

  String get code {
    switch (this) {
      case QuizLanguage.arabic:
        return 'ar';
      case QuizLanguage.english:
        return 'en';
    }
  }
}
