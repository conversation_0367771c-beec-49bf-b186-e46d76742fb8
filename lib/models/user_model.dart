// نموذج المستخدم
class UserModel {
  final String uid;
  final String email;
  final String? name;
  // تمت إزالة حقل photoUrl لأنه غير مطلوب

  UserModel({
    required this.uid,
    required this.email,
    this.name,
    // تمت إزالة photoUrl من البناء
  });

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      uid: map['uid'] ?? '',
      email: map['email'] ?? '',
      name: map['name'],
      // تمت إزالة استخراج حقل photoUrl
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'email': email,
      'name': name,
      // تمت إزالة إضافة حقل photoUrl للمستخدمين الجدد
    };
  }
}
