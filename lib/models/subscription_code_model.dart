import 'package:cloud_firestore/cloud_firestore.dart';

class SubscriptionCodeModel {
  final String id;
  final String code;
  final int durationMonths; // 1, 3, or 12 months
  final DateTime createdAt;
  final String createdBy; // Admin UID who created the code
  final bool isUsed;
  final String? usedBy; // UID of user who used the code
  final DateTime? usedAt;

  SubscriptionCodeModel({
    required this.id,
    required this.code,
    required this.durationMonths,
    required this.createdAt,
    required this.createdBy,
    this.isUsed = false,
    this.usedBy,
    this.usedAt,
  });

  factory SubscriptionCodeModel.fromMap(Map<String, dynamic> map, String documentId) {
    return SubscriptionCodeModel(
      id: documentId,
      code: map['code'] ?? '',
      durationMonths: map['durationMonths'] ?? 1,
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdBy: map['createdBy'] ?? '',
      isUsed: map['isUsed'] ?? false,
      usedBy: map['usedBy'],
      usedAt: (map['usedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'durationMonths': durationMonths,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'isUsed': isUsed,
      'usedBy': usedBy,
      'usedAt': usedAt != null ? Timestamp.fromDate(usedAt!) : null,
    };
  }

  String get durationText {
    switch (durationMonths) {
      case 1:
        return 'شهر واحد';
      case 3:
        return 'ثلاثة أشهر';
      case 12:
        return 'سنة كاملة';
      default:
        return '$durationMonths أشهر';
    }
  }
}