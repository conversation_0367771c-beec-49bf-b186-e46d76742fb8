import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';
import '../main.dart';

class UserInfoScreen extends StatefulWidget {
  @override
  _UserInfoScreenState createState() => _UserInfoScreenState();
}

class _UserInfoScreenState extends State<UserInfoScreen> {
  final AuthService _authService = AuthService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  bool _isLoading = true;
  String _userName = "";
  String _userEmail = "";
  String _userPhone = "غير محدد";
  bool _isAccountActive = true;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
  }

  Future<void> _loadUserInfo() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      // Get user information from Firebase Auth
      final user = _authService.currentUser;
      
      if (user != null) {
        // Set basic info from Auth
        _userName = user.displayName ?? "المستخدم";
        _userEmail = user.email ?? "لا يوجد بريد إلكتروني";
        
        // Get additional info from Firestore
        final userData = await _firestore
            .collection('users')
            .doc(user.uid)
            .get();
        
        if (userData.exists && userData.data() != null) {
          final data = userData.data()!;
          
          // get phone number
          if (data.containsKey('phone')) {
            var phoneValue = data['phone'];
            if (phoneValue != null && phoneValue.toString().isNotEmpty) {
              _userPhone = phoneValue.toString();
            }
          }
          
          // get account status
          _isAccountActive = data['isActive'] ?? true;
        }
      } else {
        _userName = "المستخدم";
        _userEmail = "لا يوجد بريد إلكتروني";
      }
    } catch (e) {
      print('Error loading user info: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Show message for features coming soon
  void _showFeatureComingSoonMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('سيتم تفعيل هذه الميزة قريباً'),
        backgroundColor: Color(0xFF30BEA2),
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  // عرض مربع حوار تأكيد تسجيل الخروج
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Align(
          alignment: Alignment.centerRight,
          child: Text("تسجيل الخروج"),
        ),
        content: Text("هل أنت متأكد من رغبتك في تسجيل الخروج؟"),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: Text("إلغاء"),
          ),
          TextButton(
            onPressed: () async {
              // إغلاق مربع الحوار أولاً
              Navigator.of(dialogContext).pop();
              
              // التحقق من أن الـ widget لا يزال مرتبطاً
              if (!mounted) return;
              
              setState(() {
                _isLoading = true;
              });
              
              try {
                // تسجيل الخروج
                await _authService.signOut();
                
                // استخدام navigatorKey العمومي من ملف main.dart بدلاً من context
                navigatorKey.currentState?.pushNamedAndRemoveUntil(
                  '/login',
                  (route) => false,
                );
              } catch (e) {
                // التحقق من أن الـ widget لا يزال مرتبطاً قبل تحديث الحالة
                if (!mounted) return;
                
                setState(() {
                  _isLoading = false;
                });
                
                // استخدام ScaffoldMessenger لعرض رسالة الخطأ
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('فشل تسجيل الخروج'),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                    duration: Duration(seconds: 2),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                );
              }
            },
            child: Text(
              "تسجيل الخروج",
              style: TextStyle(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "معلومات المستخدم",
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF30BEA2),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 2,
                              blurRadius: 5,
                              offset: Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            ListTile(
                              title: Text("الاسم"),
                              trailing: Text(_userName),
                            ),
                            Divider(),
                            ListTile(
                              title: Text("البريد الإلكتروني"),
                              trailing: Text(_userEmail),
                            ),
                            Divider(),
                            ListTile(
                              title: Text("رقم الهاتف"),
                              trailing: Text(
                                _userPhone != "غير محدد" ? _userPhone : "غير محدد", 
                                style: TextStyle(
                                  color: _userPhone != "غير محدد" ? Colors.black : Colors.grey,
                                  fontWeight: _userPhone != "غير محدد" ? FontWeight.normal : FontWeight.w300,
                                ),
                              ),
                              onTap: () {
                                if (_userPhone == "غير محدد") {
                                  _showFeatureComingSoonMessage();
                                }
                              },
                            ),
                            Divider(),
                            ListTile(
                              title: Text("حالة الحساب"),
                              trailing: _isAccountActive
                                ? Chip(
                                    label: Text(
                                      "مفعل",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                    backgroundColor: Colors.green,
                                  )
                                : Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Chip(
                                        label: Text(
                                          "غير مفعل",
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                          ),
                                        ),
                                        backgroundColor: Colors.red,
                                      ),
                                      SizedBox(width: 8),
                                      TextButton(
                                        onPressed: _showFeatureComingSoonMessage,
                                        child: Text(
                                          "تفعيل",
                                          style: TextStyle(
                                            color: Color(0xFF30BEA2),
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        style: TextButton.styleFrom(
                                          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(15),
                                            side: BorderSide(color: Color(0xFF30BEA2)),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                            ),
                          ],
                        ),
                      ),
                      Spacer(),
                      // Logout Button
                      Container(
                        margin: EdgeInsets.symmetric(horizontal: 40),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.red.withOpacity(0.1),
                              spreadRadius: 1,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                          border: Border.all(
                            color: Colors.red.shade200,
                            width: 1.0,
                          ),
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _showLogoutDialog,
                            borderRadius: BorderRadius.circular(15),
                            child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.logout_rounded,
                                    color: Colors.red.shade600,
                                    size: 16,
                                  ),
                                  SizedBox(width: 8),
                                  Text(
                                    "تسجيل الخروج",
                                    style: TextStyle(
                                      color: Colors.red.shade600,
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 16),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
