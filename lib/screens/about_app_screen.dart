import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutAppScreen extends StatelessWidget {
  // إضافة دالة إطلاق الروابط
  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          "حول التطبيق",
          style: TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Colors.black),
      ),
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.1),
                          spreadRadius: 2,
                          blurRadius: 5,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // وصف التطبيق مع أيقونة
                            ListTile(
                              leading: Icon(Icons.info_outline, color: Color(0xFF30BEA2)),
                              title: Text(
                                "وصف التطبيق",
                                style: TextStyle(fontWeight: FontWeight.bold)
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 8.0, right: 8.0),
                                child: Text(
                                  "كويزيمي، الحل الأمثل للطلاب والمعلمين! يحول ملفات الـ PDF إلى اختبارات تفاعلية (MCQ) بلمسة زر. وفّر وقتك وجهدك مع أحدث تقنيات الذكاء الاصطناعي لإنشاء اختبارات احترافية وسهلة الاستخدام.",
                                  style: TextStyle(height: 1.5),
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                            ),
                            Divider(),
                            // إصدار التطبيق مع أيقونة
                            ListTile(
                              leading: Icon(Icons.new_releases_outlined, color: Color(0xFF30BEA2)),
                              title: Text(
                                "إصدار التطبيق",
                                style: TextStyle(fontWeight: FontWeight.bold)
                              ),
                              trailing: Container(
                                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Color(0xFFE8F5F2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  "1.0.3",
                                  style: TextStyle(
                                    color: Color(0xFF30BEA2),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                            ),
                            Divider(),
                            // سياسة الخصوصية مع أيقونة
                            ListTile(
                              leading: Icon(Icons.privacy_tip_outlined, color: Color(0xFF30BEA2)),
                              title: Text(
                                "سياسة الخصوصية",
                                style: TextStyle(fontWeight: FontWeight.bold)
                              ),
                              trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Color(0xFF30BEA2)),
                              onTap: () => _launchURL('https://quizimy.netlify.app/'),
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                            ),
                            Divider(),
                            // حقوق النشر مع أيقونة
                            ListTile(
                              leading: Icon(Icons.copyright_outlined, color: Color(0xFF30BEA2)),
                              title: Text(
                                "حقوق النشر",
                                style: TextStyle(fontWeight: FontWeight.bold)
                              ),
                              subtitle: Padding(
                                padding: const EdgeInsets.only(top: 8.0, right: 8.0),
                                child: Text(
                                  "© 2025 - جميع الحقوق محفوظة  TQ Solutions",
                                  style: TextStyle(fontSize: 13),
                                ),
                              ),
                              contentPadding: EdgeInsets.symmetric(vertical: 8),
                            ),
                            SizedBox(height: 24),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                // Footer with the TQ Solutions text and heart icon outside the card
                Padding(
                  padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "صنع ب",
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: Icon(
                          Icons.favorite,
                          color: Colors.red,
                          size: 16,
                        ),
                      ),
                      Text(
                        " بواسطة TQ Solutions",
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
