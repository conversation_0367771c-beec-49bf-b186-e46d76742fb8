import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/subscription_service.dart';
import '../models/subscription_code_model.dart';

class CreateSubscriptionCodesScreen extends StatefulWidget {
  @override
  _CreateSubscriptionCodesScreenState createState() => _CreateSubscriptionCodesScreenState();
}

class _CreateSubscriptionCodesScreenState extends State<CreateSubscriptionCodesScreen> {
  final SubscriptionService _subscriptionService = SubscriptionService();
  bool _isLoading = false;
  List<SubscriptionCodeModel> _codes = [];
  int _selectedDuration = 1; // Default to 1 month

  @override
  void initState() {
    super.initState();
    _checkAdminAndLoadCodes();
  }

  Future<void> _checkAdminAndLoadCodes() async {
    // التحقق من أن المستخدم هو الأدمن
    if (!_subscriptionService.isCurrentUserAdmin) {
      if (mounted) {
        _showErrorSnackBar('غير مصرح لك بالوصول لهذه الصفحة');
        Navigator.pop(context);
      }
      return;
    }

    // إذا كان أدمن، حمل الأكواد
    _loadCodes();
  }

  Future<void> _loadCodes() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final codes = await _subscriptionService.getSubscriptionCodes();
      if (mounted) {
        setState(() {
          _codes = codes;
        });
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'خطأ في تحميل الأكواد';
        if (e.toString().contains('غير مصرح')) {
          errorMessage = 'غير مصرح لك بالوصول لهذه الصفحة';
        } else if (e.toString().contains('permission')) {
          errorMessage = 'لا توجد صلاحية للوصول لقاعدة البيانات';
        }
        _showErrorSnackBar(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _createCode() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final newCode = await _subscriptionService.createSubscriptionCode(_selectedDuration);
      if (mounted) {
        _showSuccessSnackBar('تم إنشاء الكود بنجاح: ${newCode.code}');
        // إعادة تحميل جميع الأكواد لضمان التحديث الصحيح
        await _loadCodes();
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'خطأ في إنشاء الكود';
        if (e.toString().contains('غير مصرح')) {
          errorMessage = 'غير مصرح لك بإنشاء أكواد الاشتراك';
        } else if (e.toString().contains('تسجيل الدخول')) {
          errorMessage = 'يجب تسجيل الدخول أولاً';
        } else if (e.toString().contains('permission')) {
          errorMessage = 'لا توجد صلاحية للوصول لقاعدة البيانات';
        }
        _showErrorSnackBar(errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteCode(String codeId) async {
    try {
      await _subscriptionService.deleteSubscriptionCode(codeId);
      _showSuccessSnackBar('تم حذف الكود بنجاح');
      // إعادة تحميل جميع الأكواد لضمان التحديث الصحيح
      await _loadCodes();
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف الكود: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Color(0xFF30BEA2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    _showSuccessSnackBar('تم نسخ الكود');
  }

  Widget _buildDurationSelector() {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر مدة الاشتراك:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildDurationOption(1, 'شهر واحد'),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildDurationOption(3, 'ثلاثة أشهر'),
              ),
              SizedBox(width: 8),
              Expanded(
                child: _buildDurationOption(12, 'سنة كاملة'),
              ),
            ],
          ),
          SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _createCode,
              style: ElevatedButton.styleFrom(
                backgroundColor: Color(0xFF30BEA2),
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      'إنشاء كود جديد',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationOption(int months, String label) {
    final isSelected = _selectedDuration == months;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDuration = months;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? Color(0xFF30BEA2) : Colors.grey[100],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Color(0xFF30BEA2) : Colors.grey[300]!,
            width: 2,
          ),
        ),
        child: Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: isSelected ? Colors.white : Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildCodeCard(SubscriptionCodeModel code) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      code.code,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF30BEA2),
                        fontFamily: 'monospace',
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'المدة: ${code.durationText}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  IconButton(
                    onPressed: () => _copyToClipboard(code.code),
                    icon: Icon(Icons.copy, color: Color(0xFF30BEA2)),
                    tooltip: 'نسخ الكود',
                  ),
                  IconButton(
                    onPressed: () => _deleteCode(code.id),
                    icon: Icon(Icons.delete, color: Colors.red),
                    tooltip: 'حذف الكود',
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Icon(
                code.isUsed ? Icons.check_circle : Icons.radio_button_unchecked,
                color: code.isUsed ? Colors.green : Colors.orange,
                size: 16,
              ),
              SizedBox(width: 4),
              Text(
                code.isUsed ? 'مستخدم' : 'غير مستخدم',
                style: TextStyle(
                  fontSize: 12,
                  color: code.isUsed ? Colors.green : Colors.orange,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Spacer(),
              Text(
                'تاريخ الإنشاء: ${_formatDate(code.createdAt)}',
                style: TextStyle(
                  fontSize: 10,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: Icon(Icons.arrow_back_ios, color: Color(0xFF30BEA2)),
                    ),
                    Expanded(
                      child: Text(
                        'إنشاء أكواد الاشتراك',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: _loadCodes,
                      icon: Icon(Icons.refresh, color: Color(0xFF30BEA2)),
                      tooltip: 'تحديث القائمة',
                    ),
                  ],
                ),
                SizedBox(height: 20),
                _buildDurationSelector(),
                SizedBox(height: 20),
                Text(
                  'الأكواد المنشأة (${_codes.length}):',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                SizedBox(height: 12),
                Expanded(
                  child: _isLoading && _codes.isEmpty
                      ? Center(
                          child: CircularProgressIndicator(
                            color: Color(0xFF30BEA2),
                          ),
                        )
                      : _codes.isEmpty
                          ? Center(
                              child: Text(
                                'لا توجد أكواد منشأة بعد',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                ),
                              ),
                            )
                          : ListView.builder(
                              itemCount: _codes.length,
                              itemBuilder: (context, index) {
                                return _buildCodeCard(_codes[index]);
                              },
                            ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
