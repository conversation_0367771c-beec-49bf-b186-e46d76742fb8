import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:math';
import 'dart:convert';
import 'package:crypto/crypto.dart';

class AppleSignInPage extends StatefulWidget {
  const AppleSignInPage({super.key});

  @override
  _AppleSignInPageState createState() => _AppleSignInPageState();
}

class _AppleSignInPageState extends State<AppleSignInPage> {
  final _firestore = FirebaseFirestore.instance;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _handleAppleSignIn();
  }

  void _showSnackBar(String message, {bool isError = false}) {
    if (!mounted) return;
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(fontFamily: 'Cairo')),
        backgroundColor: isError ? Colors.red : Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }
  
  /// إنشاء سلسلة nonce عشوائية لتأمين عملية تسجيل الدخول
  String _generateNonce([int length = 32]) {
    const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
  }

  /// إنشاء قيمة SHA256 من nonce
  String _sha256ofString(String input) {
    final bytes = utf8.encode(input);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  Future<void> _handleAppleSignIn() async {
    if (!mounted) return;
    setState(() => _isLoading = true);

    try {
      // إنشاء nonce لتأمين العملية
      final rawNonce = _generateNonce();
      final nonce = _sha256ofString(rawNonce);

      // بدء تسجيل الدخول باستخدام آبل
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
        webAuthenticationOptions: WebAuthenticationOptions(
          clientId: 'com.tqsolutions.kifaa', // استخدام معرّف الحزمة الفعلي للتطبيق
          redirectUri: Uri.parse('https://kifaa-app.firebaseapp.com/__/auth/handler'),
        ),
      );

      print('تم الحصول على بيانات الاعتماد من آبل بنجاح');

      // إنشاء اعتماد Firebase من بيانات آبل بشكل صحيح
      final oauthCredential = OAuthProvider('apple.com').credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
        rawNonce: rawNonce, // استخدام الـ nonce الأصلي
      );

      // تسجيل الدخول في Firebase
      final UserCredential userCredential = 
          await FirebaseAuth.instance.signInWithCredential(oauthCredential);
      
      final User? user = userCredential.user;
      
      if (user != null) {
        // استخراج الاسم من بيانات آبل (قد يكون فارغًا في بعض الحالات)
        String? displayName;
        if (appleCredential.givenName != null && appleCredential.familyName != null) {
          displayName = '${appleCredential.givenName} ${appleCredential.familyName}';
        }

        // البحث عن المستخدم باستخدام البريد الإلكتروني
        final userDocQuery = await _firestore.collection('users')
            .where('email', isEqualTo: user.email)
            .limit(1)
            .get();

        Map<String, dynamic> userData = {
          'username': displayName ?? user.displayName ?? user.email?.split('@')[0] ?? 'مستخدم',
          'email': user.email,
          'phone': '', // إضافة حقل هاتف فارغ لتجنب الأخطاء
          'lastLogin': FieldValue.serverTimestamp(),
          'emailVerified': true,
        };

        // إذا كان المستخدم موجوداً، احتفظ بحالة الاشتراك المدفوع
        if (userDocQuery.docs.isNotEmpty) {
          DocumentSnapshot existingDoc = userDocQuery.docs.first;
          Map<String, dynamic> existingData = existingDoc.data() as Map<String, dynamic>;
          
          // استخدام معرّف المستند الحالي
          await _firestore.collection('users').doc(existingDoc.id).update({
            ...userData,
            // الاحتفاظ بحالة الاشتراك المدفوع إن وجدت
            'isPremium': existingData['isPremium'] ?? false,
            // نتأكد من وجود حقل الهاتف حتى لو كان فارغاً
            'phone': existingData['phone'] ?? '',
          });
        } else {
          // إنشاء مستخدم جديد باستخدام UID كمعرّف
          await _firestore.collection('users').doc(user.uid).set({
            ...userData,
            'isPremium': false,
            'examAttempts': {},
            'phone': '', // تأكيد إضافة حقل هاتف فارغ للمستخدمين الجدد
            'createdAt': FieldValue.serverTimestamp(),
          });
        }

        if (mounted) {
          _showSnackBar('تم تسجيل الدخول بنجاح');
          
          // تأخير قصير قبل الانتقال
          await Future.delayed(Duration(milliseconds: 500));
          
          if (mounted) {
            // استخدام المسار المسمى للتوجيه إلى الصفحة الرئيسية
            Navigator.of(context).pushReplacementNamed('/home');
          }
        }
      }
    } catch (e) {
      print('خطأ في تسجيل الدخول بحساب آبل: $e');
      if (mounted) {
        _showSnackBar('حدث خطأ أثناء تسجيل الدخول، يرجى المحاولة مرة أخرى.', isError: true);
        Navigator.pop(context);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 20),
                  Text(
                    'جاري تسجيل الدخول باستخدام حساب Apple...',
                    style: TextStyle(
                      fontFamily: 'Cairo',
                      fontSize: 16,
                    ),
                  ),
                ],
              )
            : Text('جاري التحميل...', style: TextStyle(fontFamily: 'Cairo')),
      ),
    );
  }
}