import 'package:flutter/material.dart';
import '../services/auth_service.dart';
import '../services/subscription_service.dart';
import '../main.dart';
import 'package:url_launcher/url_launcher.dart';
import 'user_info_screen.dart';
import 'about_app_screen.dart';
import 'contact_us_screen.dart';
import 'create_subscription_codes_screen.dart';

class SettingsScreen extends StatefulWidget {
  @override
  _SettingsScreenState createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final AuthService _authService = AuthService();
  final SubscriptionService _subscriptionService = SubscriptionService();
  bool _isLoading = true;
  String _userName = "";
  String _userEmail = "";
  bool _isAdmin = false;

  @override
  void initState() {
    super.initState();
    _loadBasicUserInfo();
  }

  Future<void> _loadBasicUserInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user information from Firebase Auth only
      final user = _authService.currentUser;

      if (user != null) {
        setState(() {
          _userName = user.displayName ?? "المستخدم";
          _userEmail = user.email ?? "لا يوجد بريد إلكتروني";
          _isAdmin = _subscriptionService.isCurrentUserAdmin;
        });
      } else {
        setState(() {
          _userName = "المستخدم";
          _userEmail = "لا يوجد بريد إلكتروني";
          _isAdmin = false;
        });
      }
    } catch (e) {
      print('Error loading basic user info: $e');
      setState(() {
        _userName = "المستخدم";
        _userEmail = "لا يوجد بريد إلكتروني";
        _isAdmin = false;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Align(
          alignment: Alignment.centerRight,
          child: Text("تسجيل الخروج"),
        ),
        content: Text("هل أنت متأكد من رغبتك في تسجيل الخروج؟", textDirection: TextDirection.rtl),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
            child: Text("إلغاء"),
          ),
          TextButton(
            onPressed: () async {
              // إغلاق مربع الحوار أولاً
              Navigator.of(dialogContext).pop();

              // التحقق من أن الـ widget لا يزال مرتبطاً
              if (!mounted) return;

              setState(() {
                _isLoading = true;
              });

              try {
                // تسجيل الخروج
                await _authService.signOut();

                // استخدام navigatorKey العمومي من ملف main.dart بدلاً من context
                navigatorKey.currentState?.pushNamedAndRemoveUntil(
                  '/login',
                  (route) => false,
                );
              } catch (e) {
                // التحقق من أن الـ widget لا يزال مرتبطاً قبل تحديث الحالة
                if (!mounted) return;

                setState(() {
                  _isLoading = false;
                });

                // استخدام ScaffoldMessenger العمومي بدلاً من context للتنبيهات
                ScaffoldMessenger.of(navigatorKey.currentContext!).showSnackBar(
                  SnackBar(
                    content: Text('حدث خطأ أثناء تسجيل الخروج'),
                    backgroundColor: Colors.red,
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            child: Text(
              "تسجيل الخروج",
              style: TextStyle(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsCard({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    required String subtitle,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Color(0xFFE8F5F2),
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Icon(
                  icon,
                  color: Color(0xFF30BEA2),
                  size: 24,
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: Color(0xFF30BEA2),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF30BEA2),
                    ),
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "الإعدادات",
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.logout,
                              color: Colors.red,
                            ),
                            onPressed: _showLogoutDialog,
                          ),
                        ],
                      ),
                      SizedBox(height: 20),
                      // صورة المستخدم والاسم والبريد
                      Container(
                        padding: EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.grey.withOpacity(0.1),
                              spreadRadius: 2,
                              blurRadius: 5,
                              offset: Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundColor: Color(0xFFE8F5F2),
                              child: Icon(
                                Icons.person,
                                size: 40,
                                color: Color(0xFF30BEA2),
                              ),
                            ),
                            SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _userName,
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  SizedBox(height: 4),
                                  Text(
                                    _userEmail,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 25),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              _buildSettingsCard(
                                title: "معلومات المستخدم",
                                icon: Icons.person_outline,
                                subtitle: "الاسم، البريد الإلكتروني، رقم الهاتف، حالة الحساب",
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => UserInfoScreen(),
                                    ),
                                  );
                                },
                              ),
                              // عرض خيار إنشاء أكواد الاشتراك للأدمن فقط
                              if (_isAdmin)
                                _buildSettingsCard(
                                  title: "إنشاء أكواد الاشتراك",
                                  icon: Icons.card_giftcard,
                                  subtitle: "إنشاء وإدارة أكواد الاشتراك المدفوع",
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => CreateSubscriptionCodesScreen(),
                                      ),
                                    );
                                  },
                                ),
                              _buildSettingsCard(
                                title: "تواصل معنا",
                                icon: Icons.support_agent,
                                subtitle: "واتس اب، تيليجرام، البريد الإلكتروني",
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => ContactUsScreen(),
                                    ),
                                  );
                                },
                              ),
                              _buildSettingsCard(
                                title: "حول التطبيق",
                                icon: Icons.info_outline,
                                subtitle: "الوصف، الإصدار، حقوق النشر",
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => AboutAppScreen(),
                                    ),
                                  );
                                },
                              ),
                              _buildSettingsCard(
                                title: "سياسة الخصوصية",
                                icon: Icons.privacy_tip_outlined,
                                subtitle: "معلومات حول سياسة الخصوصية وحماية البيانات",
                                onTap: () async {
                                  final Uri uri = Uri.parse('https://quizimy.netlify.app/');
                                  if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text('لا يمكن فتح الرابط'),
                                        backgroundColor: Colors.red,
                                        behavior: SnackBarBehavior.floating,
                                      ),
                                    );
                                  }
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 8),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
