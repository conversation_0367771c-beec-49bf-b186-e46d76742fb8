import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

class PremiumService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// التحقق من حالة الاشتراك المدفوع للمستخدم الحالي
  Future<bool> isPremiumUser() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    try {
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      
      if (!userDoc.exists) return false;
      
      final userData = userDoc.data()!;
      final isPremium = userData['isPremium'] ?? false;
      
      if (!isPremium) return false;
      
      // التحقق من تاريخ انتهاء الاشتراك
      final premiumUntil = userData['premiumUntil'] as Timestamp?;
      if (premiumUntil == null) return false;
      
      return premiumUntil.toDate().isAfter(DateTime.now());
    } catch (e) {
      print('خطأ في التحقق من حالة الاشتراك: $e');
      return false;
    }
  }

  /// التحقق من استخدام التجربة المجانية
  Future<bool> hasUsedFreeTrial() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return false;

    try {
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      
      if (!userDoc.exists) return false;
      
      final userData = userDoc.data()!;
      return userData['hasUsedFreeTrial'] ?? false;
    } catch (e) {
      print('خطأ في التحقق من التجربة المجانية: $e');
      return false;
    }
  }

  /// تسجيل استخدام التجربة المجانية
  Future<void> markFreeTrialAsUsed() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      await _firestore.collection('users').doc(currentUser.uid).update({
        'hasUsedFreeTrial': true,
        'freeTrialUsedAt': Timestamp.now(),
      });
    } catch (e) {
      print('خطأ في تسجيل استخدام التجربة المجانية: $e');
    }
  }

  /// التحقق من إمكانية إنشاء اختبار جديد
  Future<bool> canCreateQuiz() async {
    // إذا كان مستخدم مدفوع، يمكنه إنشاء اختبارات غير محدودة
    if (await isPremiumUser()) {
      return true;
    }

    // إذا لم يستخدم التجربة المجانية بعد، يمكنه إنشاء اختبار واحد
    return !(await hasUsedFreeTrial());
  }

  /// الحصول على معلومات الاشتراك
  Future<Map<String, dynamic>> getSubscriptionInfo() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      return {
        'isPremium': false,
        'hasUsedFreeTrial': false,
        'premiumUntil': null,
      };
    }

    try {
      final userDoc = await _firestore.collection('users').doc(currentUser.uid).get();
      
      if (!userDoc.exists) {
        return {
          'isPremium': false,
          'hasUsedFreeTrial': false,
          'premiumUntil': null,
        };
      }
      
      final userData = userDoc.data()!;
      final premiumUntil = userData['premiumUntil'] as Timestamp?;
      
      return {
        'isPremium': userData['isPremium'] ?? false,
        'hasUsedFreeTrial': userData['hasUsedFreeTrial'] ?? false,
        'premiumUntil': premiumUntil?.toDate(),
        'freeTrialUsedAt': (userData['freeTrialUsedAt'] as Timestamp?)?.toDate(),
      };
    } catch (e) {
      print('خطأ في الحصول على معلومات الاشتراك: $e');
      return {
        'isPremium': false,
        'hasUsedFreeTrial': false,
        'premiumUntil': null,
      };
    }
  }

  /// الحصول على عدد الأيام المتبقية في الاشتراك
  Future<int?> getDaysRemaining() async {
    if (!(await isPremiumUser())) return null;

    final subscriptionInfo = await getSubscriptionInfo();
    final premiumUntil = subscriptionInfo['premiumUntil'] as DateTime?;
    
    if (premiumUntil == null) return null;
    
    final difference = premiumUntil.difference(DateTime.now());
    return difference.inDays;
  }

  /// تفعيل كود الاشتراك
  Future<bool> activateSubscriptionCode(String code) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      // البحث عن الكود
      final querySnapshot = await _firestore
          .collection('subscription_codes')
          .where('code', isEqualTo: code.toUpperCase())
          .where('isUsed', isEqualTo: false)
          .get();

      if (querySnapshot.docs.isEmpty) {
        throw Exception('كود الاشتراك غير صحيح أو مستخدم مسبقاً');
      }

      final codeDoc = querySnapshot.docs.first;
      final codeData = codeDoc.data();
      final durationMonths = codeData['durationMonths'] as int;

      // تحديث حالة الكود إلى مستخدم
      await _firestore.collection('subscription_codes').doc(codeDoc.id).update({
        'isUsed': true,
        'usedBy': currentUser.uid,
        'usedAt': Timestamp.now(),
      });

      // تحديث اشتراك المستخدم
      await _updateUserSubscription(currentUser.uid, durationMonths);

      return true;
    } catch (e) {
      print('خطأ في تفعيل كود الاشتراك: $e');
      rethrow;
    }
  }

  /// تحديث اشتراك المستخدم
  Future<void> _updateUserSubscription(String userId, int durationMonths) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      throw Exception('المستخدم غير موجود');
    }

    final userData = userDoc.data()!;
    final currentPremiumUntil = userData['premiumUntil'] as Timestamp?;

    DateTime newPremiumUntil;
    if (currentPremiumUntil != null && currentPremiumUntil.toDate().isAfter(DateTime.now())) {
      // إذا كان لديه اشتراك ساري، أضف المدة الجديدة
      newPremiumUntil = currentPremiumUntil.toDate().add(Duration(days: durationMonths * 30));
    } else {
      // إذا لم يكن لديه اشتراك ساري، ابدأ من الآن
      newPremiumUntil = DateTime.now().add(Duration(days: durationMonths * 30));
    }

    await _firestore.collection('users').doc(userId).update({
      'isPremium': true,
      'premiumUntil': Timestamp.fromDate(newPremiumUntil),
      'subscriptionActivatedAt': Timestamp.now(),
    });
  }
}
