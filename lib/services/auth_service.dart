import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../models/user_model.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // تدفق حالة المستخدم - يتغير عندما تتغير حالة تسجيل الدخول
  Stream<User?> get authStateChanges => _auth.authStateChanges();
  
  // الحصول على المستخدم الحالي
  User? get currentUser => _auth.currentUser;
  
  // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential> signInWithEmailAndPassword(String email, String password) async {
    try {
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // إضافة أو تحديث معلومات المستخدم في Firestore
      await _saveUserToFirestore(userCredential.user);
      
      return userCredential;
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      rethrow;
    }
  }
  
  // إنشاء حساب جديد بالبريد الإلكتروني وكلمة المرور
  Future<UserCredential> createUserWithEmailAndPassword(String email, String password, String name) async {
    try {
      // إنشاء حساب جديد
      UserCredential userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      
      // تحديث اسم المستخدم في Firebase Auth
      await userCredential.user?.updateDisplayName(name);
      
      // لضمان تحديث البيانات بشكل صحيح
      await userCredential.user?.reload();
      
      // إضافة معلومات المستخدم إلى Firestore
      await _saveUserToFirestore(userCredential.user, name: name);
      
      return userCredential;
    } on FirebaseAuthException catch (e) {
      print('خطأ في إنشاء الحساب: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      print('خطأ غير متوقع: $e');
      rethrow;
    }
  }
  
  // تسجيل الدخول باستخدام حساب Google
  Future<UserCredential> signInWithGoogle() async {
    try {
      // بدء عملية تسجيل الدخول باستخدام Google
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      if (googleUser == null) {
        throw FirebaseAuthException(
          code: 'google-sign-in-canceled',
          message: 'تم إلغاء تسجيل الدخول باستخدام Google',
        );
      }
      
      // الحصول على بيانات المصادقة من طلب الدخول
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      
      // إنشاء بيانات اعتماد جديدة للمستخدم
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );
      
      // تسجيل الدخول باستخدام البيانات
      UserCredential userCredential = await _auth.signInWithCredential(credential);
      
      // إضافة معلومات المستخدم إلى Firestore
      await _saveUserToFirestore(userCredential.user);
      
      return userCredential;
    } catch (e) {
      print('خطأ في تسجيل الدخول بواسطة Google: $e');
      rethrow;
    }
  }
  
  // حفظ معلومات المستخدم في Firestore
  Future<void> _saveUserToFirestore(User? user, {String? name}) async {
    if (user == null) return;
    
    try {
      // التحقق مما إذا كان المستخدم موجودًا بالفعل
      DocumentSnapshot doc = await _firestore.collection('users').doc(user.uid).get();
      
      // التأكد من أن البيانات مملوءة بشكل صحيح
      final String userName = name ?? user.displayName ?? 'مستخدم جديد';
      final String userEmail = user.email ?? '';
      
      if (!doc.exists) {
        // إنشاء نموذج مستخدم
        UserModel userModel = UserModel(
          uid: user.uid,
          email: userEmail,
          name: userName,
          // تمت إزالة photoUrl
        );
        
        // حفظ المستخدم في Firestore
        await _firestore.collection('users').doc(user.uid).set(userModel.toMap());
        print('تم حفظ بيانات المستخدم في Firestore: ${user.uid}');
      } else {
        // تحديث البيانات الموجودة
        await _firestore.collection('users').doc(user.uid).update({
          'name': userName,
          'email': userEmail,
          // تمت إزالة photoUrl عند التحديث
        });
        print('تم تحديث بيانات المستخدم في Firestore: ${user.uid}');
      }
    } catch (e) {
      print('خطأ في حفظ بيانات المستخدم: $e');
      // لا نقوم بإعادة رمي الخطأ هنا لتجنب إيقاف عملية تسجيل الدخول
    }
  }
  
  // تسجيل الخروج
  Future<void> signOut() async {
    try {
      // التحقق من تسجيل الخروج من Google إذا كان المستخدم مسجلًا عبر Google
      final GoogleSignIn googleSignIn = GoogleSignIn();
      if (await googleSignIn.isSignedIn()) {
        await googleSignIn.signOut();
      }
      
      // تسجيل الخروج من Firebase
      await _auth.signOut();
    } catch (e) {
      print('خطأ في تسجيل الخروج: $e');
      rethrow;
    }
  }
  
  // إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    await _auth.sendPasswordResetEmail(email: email);
  }
  
  // تحديث معلومات المستخدم
  Future<void> updateUserProfile({String? name}) async {
    User? user = _auth.currentUser;
    if (user != null) {
      if (name != null) {
        await user.updateDisplayName(name);
      }
      
      // تحديث المعلومات في Firestore
      await _firestore.collection('users').doc(user.uid).update({
        if (name != null) 'name': name,
        // تمت إزالة تحديث photoUrl
      });
    }
  }
}
