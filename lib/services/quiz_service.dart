import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/quiz_model.dart';
import 'package:google_generative_ai/google_generative_ai.dart';

class QuizService {
  // حفظ الاختبارات في الذاكرة المحلية
  Future<void> saveQuizzes(List<Quiz> quizzes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = quizzes
          .map((quiz) => jsonEncode(quiz.toJson()))
          .toList();
      
      await prefs.setStringList('quizzes', quizzesJson);
    } catch (e) {
      print('Error saving quizzes: $e');
      throw Exception('فشل في حفظ الاختبارات: $e');
    }
  }
  
  // استرجاع الاختبارات من الذاكرة المحلية
  Future<List<Quiz>> loadQuizzes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quizzesJson = prefs.getStringList('quizzes') ?? [];
      
      return quizzesJson
          .map((json) => Quiz.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      print('Error loading quizzes: $e');
      return [];
    }
  }
  
  // إنشاء الاختبارات باستخدام الذكاء الاصطناعي
  Future<List<Question>> generateQuizQuestions(
    String pdfText,
    int questionCount,
    QuizDifficulty difficulty,
    QuizLanguage language,
    {Function(String)? onProgress}
  ) async {
    try {
      onProgress?.call('بدء إنشاء الاختبار...');

      // تحليل وتحسين النص للكتب الأكاديمية
      String processedText = _analyzeAndOptimizeText(pdfText, onProgress);

      if (processedText.length > 25000) {
        onProgress?.call('تحسين النص للمعالجة...');
        processedText = _smartTextTruncation(processedText, 25000);
      }

      onProgress?.call('الاتصال بالذكاء الاصطناعي...');
      final model = GenerativeModel(
        model: 'gemini-2.0-flash',
        apiKey: 'AIzaSyBExV2C3FZSlmMmakl0vBre0PvkMlqPpfU',
      );
      
      // تحديد مستوى الصعوبة واللغة في الطلب إلى الذكاء الاصطناعي
      String difficultyText = '';
      String languageInstruction = '';
      String exampleFormat = '';

      // تحديد مستوى الصعوبة
      switch (difficulty) {
        case QuizDifficulty.easy:
          difficultyText = language == QuizLanguage.arabic
              ? 'سهلة وبسيطة تناسب المبتدئين'
              : 'easy and simple, suitable for beginners';
          break;
        case QuizDifficulty.medium:
          difficultyText = language == QuizLanguage.arabic
              ? 'متوسطة الصعوبة تناسب المستوى المتوسط'
              : 'medium difficulty, suitable for intermediate level';
          break;
        case QuizDifficulty.hard:
          difficultyText = language == QuizLanguage.arabic
              ? 'صعبة ومتقدمة تتطلب معرفة عميقة بالموضوع'
              : 'hard and advanced, requiring deep knowledge of the subject';
          break;
      }

      // تحديد تعليمات اللغة والتنسيق
      if (language == QuizLanguage.arabic) {
        languageInstruction = '''
أنت خبير في إعداد الامتحانات التنافسية للطلاب الجامعيين وطلاب الماجستير.
أنشئ اختبارًا أكاديميًا متخصصًا بناءً على المحتوى التالي. قم بإنشاء $questionCount أسئلة متعددة الخيارات مع 4 خيارات لكل سؤال.

متطلبات الاختبار:
- الأسئلة يجب أن تكون بمستوى $difficultyText
- جميع الأسئلة والخيارات يجب أن تكون باللغة العربية
- ركز على المفاهيم الأساسية، التعريفات، النظريات، والتطبيقات العملية
- اجعل الأسئلة مشابهة لأسئلة الامتحانات التنافسية الجامعية
- تأكد من أن الخيارات الخاطئة منطقية ومعقولة لتحدي الطالب
- اختبر الفهم العميق وليس فقط الحفظ
- استخدم صيغ متنوعة: "ما هو"، "أي من التالي"، "يمكن القول أن"، "من خصائص"

قم بتنسيق إجابتك بتنسيق JSON بالضبط كما هو موضح في المثال. تأكد من أن الإجابة الصحيحة هي دائمًا خيار واحد فقط (0 أو 1 أو 2 أو 3).''';

        exampleFormat = '''
أريد الإجابة بهذا التنسيق بالضبط (JSON فقط، بدون أي نص إضافي):
[
  {
    "question": "نص السؤال الأول هنا؟",
    "options": ["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "الخيار الرابع"],
    "correctAnswerIndex": 0
  },
  {
    "question": "نص السؤال الثاني هنا؟",
    "options": ["الخيار الأول", "الخيار الثاني", "الخيار الثالث", "الخيار الرابع"],
    "correctAnswerIndex": 2
  }
]''';
      } else {
        languageInstruction = '''
You are an expert in preparing competitive exams for university and graduate students.
Create a specialized academic quiz based on the following content. Generate $questionCount multiple choice questions with 4 options each.

Quiz Requirements:
- Questions should be at $difficultyText level
- All questions and options must be in English
- Focus on key concepts, definitions, theories, and practical applications
- Make questions similar to university competitive exam questions
- Ensure incorrect options are logical and reasonable to challenge students
- Test deep understanding, not just memorization
- Use varied question formats: "What is", "Which of the following", "It can be said that", "Characteristics include"

Format your response in JSON exactly as shown in the example. Make sure the correct answer is always only one option (0 or 1 or 2 or 3).''';

        exampleFormat = '''
I want the answer in this exact format (JSON only, without any additional text):
[
  {
    "question": "First question text here?",
    "options": ["Option one", "Option two", "Option three", "Option four"],
    "correctAnswerIndex": 0
  },
  {
    "question": "Second question text here?",
    "options": ["Option one", "Option two", "Option three", "Option four"],
    "correctAnswerIndex": 2
  }
]''';
      }
      
      final prompt = '''
$languageInstruction

المحتوى:
$processedText

$exampleFormat
''';

      onProgress?.call('جاري تجهيز الاختبار يرجى الانتظار...');
      final content = [Content.text(prompt)];
      final response = await model.generateContent(content);

      onProgress?.call('تحليل الاستجابة...');
      final jsonResponse = response.text;
      if (jsonResponse == null || jsonResponse.isEmpty) {
        throw Exception('فشل في إنشاء الأسئلة');
      }

      onProgress?.call('معالجة البيانات المستلمة...');
      // تنظيف نص JSON (إزالة أي محتوى غير JSON)
      String cleanedJson = jsonResponse.trim();
      // إزالة علامات كتلة التعليمات البرمجية
      if (cleanedJson.startsWith('```json')) {
        cleanedJson = cleanedJson.substring(7);
      }
      if (cleanedJson.startsWith('```')) {
        cleanedJson = cleanedJson.substring(3);
      }
      if (cleanedJson.endsWith('```')) {
        cleanedJson = cleanedJson.substring(0, cleanedJson.length - 3);
      }
      cleanedJson = cleanedJson.trim();
      
      onProgress?.call('إنشاء الأسئلة...');
      // تحليل استجابة JSON
      List<dynamic> questionsJson = jsonDecode(cleanedJson);

      // التحويل إلى كائنات Question
      List<Question> questions = questionsJson.map((q) {
        return Question(
          question: q['question'],
          options: List<String>.from(q['options']),
          correctAnswerIndex: q['correctAnswerIndex'],
        );
      }).toList();

      onProgress?.call('تم إنشاء ${questions.length} سؤال بنجاح!');
      return questions;
    } catch (e) {
      print('Error generating quiz: $e');
      throw Exception('فشل في إنشاء الاختبار: $e');
    }
  }

  /// تحليل وتحسين النص للكتب الأكاديمية
  String _analyzeAndOptimizeText(String text, Function(String)? onProgress) {
    onProgress?.call('تحليل محتوى الكتاب الأكاديمي...');

    // إزالة الأسطر الفارغة المتكررة والمسافات الزائدة
    String cleanedText = text
        .replaceAll(RegExp(r'\n\s*\n\s*\n'), '\n\n') // تقليل الأسطر الفارغة المتكررة
        .replaceAll(RegExp(r'[ \t]+'), ' ') // توحيد المسافات
        .trim();

    // تحديد الأقسام المهمة (العناوين، الفقرات الرئيسية)
    List<String> importantSections = [];
    List<String> lines = cleanedText.split('\n');

    onProgress?.call('استخراج المفاهيم الأساسية...');

    for (int i = 0; i < lines.length; i++) {
      String line = lines[i].trim();

      // تخطي الأسطر الفارغة أو القصيرة جداً
      if (line.length < 10) continue;

      // إعطاء أولوية للعناوين والنقاط المهمة
      if (_isImportantLine(line)) {
        importantSections.add(line);

        // إضافة السياق (الفقرة التالية إذا كانت مفيدة)
        if (i + 1 < lines.length) {
          String nextLine = lines[i + 1].trim();
          if (nextLine.length > 20 && !_isImportantLine(nextLine)) {
            importantSections.add(nextLine);
          }
        }
      }
      // إضافة الفقرات الطويلة والمفيدة
      else if (line.length > 50 && _containsAcademicContent(line)) {
        importantSections.add(line);
      }
    }

    onProgress?.call('تنظيم المحتوى للاختبار...');

    // دمج الأقسام المهمة
    String optimizedText = importantSections.join('\n\n');

    // إذا كان النص المحسن قصير جداً، استخدم النص الأصلي مع تحسينات بسيطة
    if (optimizedText.length < 1000) {
      return cleanedText;
    }

    return optimizedText;
  }

  /// تحديد ما إذا كان السطر مهماً (عنوان، نقطة رئيسية، إلخ)
  bool _isImportantLine(String line) {
    // العناوين والنقاط المرقمة
    if (RegExp(r'^[\d\u0660-\u0669]+[\.\-\)]').hasMatch(line)) return true;
    if (RegExp(r'^[أ-ي\u0621-\u064A]+[\.\-\)]').hasMatch(line)) return true;

    // الكلمات المفتاحية المهمة
    List<String> keywords = [
      'تعريف', 'مفهوم', 'أهمية', 'خصائص', 'أنواع', 'تصنيف',
      'مبادئ', 'قوانين', 'نظرية', 'فرضية', 'استنتاج', 'خلاصة',
      'definition', 'concept', 'importance', 'characteristics', 'types',
      'principles', 'laws', 'theory', 'hypothesis', 'conclusion'
    ];

    for (String keyword in keywords) {
      if (line.toLowerCase().contains(keyword.toLowerCase())) {
        return true;
      }
    }

    // الأسطر القصيرة التي قد تكون عناوين
    if (line.length < 100 && line.length > 10) {
      // تحقق من عدم وجود علامات ترقيم كثيرة (قد تكون عنوان)
      int punctuationCount = RegExp(r'[\.،؛:]').allMatches(line).length;
      if (punctuationCount <= 1) return true;
    }

    return false;
  }

  /// تحديد ما إذا كان السطر يحتوي على محتوى أكاديمي مفيد
  bool _containsAcademicContent(String line) {
    // تجنب الأسطر التي تحتوي على أرقام صفحات أو مراجع فقط
    if (RegExp(r'^\s*[\d\u0660-\u0669]+\s*$').hasMatch(line)) return false;
    if (RegExp(r'^\s*صفحة\s*[\d\u0660-\u0669]+').hasMatch(line)) return false;

    // البحث عن محتوى أكاديمي مفيد
    List<String> academicIndicators = [
      'يمكن', 'يجب', 'ينبغي', 'يعتبر', 'يشير', 'يوضح', 'يبين',
      'من خلال', 'بناء على', 'وفقاً', 'حسب', 'طبقاً',
      'can', 'should', 'must', 'indicates', 'shows', 'according'
    ];

    for (String indicator in academicIndicators) {
      if (line.toLowerCase().contains(indicator.toLowerCase())) {
        return true;
      }
    }

    // الفقرات التي تحتوي على معلومات وصفية أو تفسيرية
    return line.contains('هو') || line.contains('هي') ||
           line.contains('التي') || line.contains('الذي') ||
           line.contains('is') || line.contains('are') ||
           line.contains('that') || line.contains('which');
  }

  /// اقتطاع ذكي للنص مع الحفاظ على المعنى
  String _smartTextTruncation(String text, int maxLength) {
    if (text.length <= maxLength) return text;

    // البحث عن نقطة قطع مناسبة (نهاية فقرة أو جملة)
    int cutPoint = maxLength;

    // البحث عن نهاية فقرة قريبة
    for (int i = maxLength - 500; i < maxLength && i < text.length; i++) {
      if (text[i] == '\n' && i + 1 < text.length && text[i + 1] == '\n') {
        cutPoint = i;
        break;
      }
    }

    // إذا لم نجد نهاية فقرة، ابحث عن نهاية جملة
    if (cutPoint == maxLength) {
      for (int i = maxLength - 200; i < maxLength && i < text.length; i++) {
        if (text[i] == '.' || text[i] == '؟' || text[i] == '!') {
          cutPoint = i + 1;
          break;
        }
      }
    }

    return '${text.substring(0, cutPoint)}\n\n[تم اقتطاع النص مع الحفاظ على المحتوى الأساسي للحصول على أفضل النتائج...]';
  }

}
