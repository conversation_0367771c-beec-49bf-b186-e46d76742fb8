import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/subscription_code_model.dart';

class SubscriptionService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // UID الأدمن المحدد
  static const String ADMIN_UID = 'H4OwuI6NYYdhcaE8EOotikH4Fhf1';

  /// التحقق من أن المستخدم الحالي هو الأدمن
  bool get isCurrentUserAdmin {
    final currentUser = _auth.currentUser;
    return currentUser != null && currentUser.uid == ADMIN_UID;
  }

  /// إنشاء كود اشتراك جديد
  Future<SubscriptionCodeModel> createSubscriptionCode(int durationMonths) async {
    if (!isCurrentUserAdmin) {
      throw Exception('غير مصرح لك بإنشاء أكواد الاشتراك');
    }

    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      // إنشاء كود فريد
      final code = _generateUniqueCode();

      // التحقق من عدم وجود الكود مسبقاً
      final existingCode = await _firestore
          .collection('subscription_codes')
          .where('code', isEqualTo: code)
          .get();

      if (existingCode.docs.isNotEmpty) {
        // إذا كان الكود موجود، أنشئ كود جديد
        return createSubscriptionCode(durationMonths);
      }

      final subscriptionCode = SubscriptionCodeModel(
        id: '', // سيتم تعيينه من Firestore
        code: code,
        durationMonths: durationMonths,
        createdAt: DateTime.now(),
        createdBy: currentUser.uid,
      );

      // حفظ الكود في Firestore
      final docRef = await _firestore
          .collection('subscription_codes')
          .add(subscriptionCode.toMap());

      return SubscriptionCodeModel(
        id: docRef.id,
        code: subscriptionCode.code,
        durationMonths: subscriptionCode.durationMonths,
        createdAt: subscriptionCode.createdAt,
        createdBy: subscriptionCode.createdBy,
      );
    } catch (e) {
      print('خطأ في إنشاء كود الاشتراك: $e');
      throw Exception('فشل في إنشاء كود الاشتراك: ${e.toString()}');
    }
  }

  /// إنشاء كود فريد
  String _generateUniqueCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();

    // إنشاء كود من 8 أحرف
    String code = '';
    for (int i = 0; i < 8; i++) {
      code += chars[random.nextInt(chars.length)];
    }

    // تنسيق الكود بشكل XXXX-XXXX
    return '${code.substring(0, 4)}-${code.substring(4, 8)}';
  }

  /// الحصول على جميع أكواد الاشتراك التي أنشأها الأدمن
  Future<List<SubscriptionCodeModel>> getSubscriptionCodes() async {
    if (!isCurrentUserAdmin) {
      throw Exception('غير مصرح لك بعرض أكواد الاشتراك');
    }

    try {
      // استعلام بسيط بدون ترتيب لتجنب مشكلة الفهرس
      final querySnapshot = await _firestore
          .collection('subscription_codes')
          .where('createdBy', isEqualTo: ADMIN_UID)
          .get();

      // تحويل البيانات وترتيبها محلياً
      List<SubscriptionCodeModel> codes = querySnapshot.docs
          .map((doc) => SubscriptionCodeModel.fromMap(doc.data(), doc.id))
          .toList();

      // ترتيب الأكواد حسب تاريخ الإنشاء (الأحدث أولاً)
      codes.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      return codes;
    } catch (e) {
      print('خطأ في تحميل أكواد الاشتراك: $e');
      // إذا كانت المجموعة غير موجودة، أرجع قائمة فارغة
      return [];
    }
  }

  /// استخدام كود الاشتراك
  Future<bool> useSubscriptionCode(String code) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    // البحث عن الكود
    final querySnapshot = await _firestore
        .collection('subscription_codes')
        .where('code', isEqualTo: code.toUpperCase())
        .where('isUsed', isEqualTo: false)
        .get();

    if (querySnapshot.docs.isEmpty) {
      throw Exception('كود الاشتراك غير صحيح أو مستخدم مسبقاً');
    }

    final codeDoc = querySnapshot.docs.first;
    final subscriptionCode = SubscriptionCodeModel.fromMap(codeDoc.data(), codeDoc.id);

    // تحديث حالة الكود إلى مستخدم
    await _firestore.collection('subscription_codes').doc(codeDoc.id).update({
      'isUsed': true,
      'usedBy': currentUser.uid,
      'usedAt': Timestamp.now(),
    });

    // تحديث اشتراك المستخدم
    await _updateUserSubscription(currentUser.uid, subscriptionCode.durationMonths);

    return true;
  }

  /// تحديث اشتراك المستخدم
  Future<void> _updateUserSubscription(String userId, int durationMonths) async {
    final userDoc = await _firestore.collection('users').doc(userId).get();

    if (!userDoc.exists) {
      throw Exception('المستخدم غير موجود');
    }

    final userData = userDoc.data()!;
    final currentPremiumUntil = userData['premiumUntil'] as Timestamp?;

    DateTime newPremiumUntil;
    if (currentPremiumUntil != null && currentPremiumUntil.toDate().isAfter(DateTime.now())) {
      // إذا كان لديه اشتراك ساري، أضف المدة الجديدة
      newPremiumUntil = currentPremiumUntil.toDate().add(Duration(days: durationMonths * 30));
    } else {
      // إذا لم يكن لديه اشتراك ساري، ابدأ من الآن
      newPremiumUntil = DateTime.now().add(Duration(days: durationMonths * 30));
    }

    await _firestore.collection('users').doc(userId).update({
      'isPremium': true,
      'premiumUntil': Timestamp.fromDate(newPremiumUntil),
    });
  }

  /// حذف كود اشتراك
  Future<void> deleteSubscriptionCode(String codeId) async {
    if (!isCurrentUserAdmin) {
      throw Exception('غير مصرح لك بحذف أكواد الاشتراك');
    }

    await _firestore.collection('subscription_codes').doc(codeId).delete();
  }
}
