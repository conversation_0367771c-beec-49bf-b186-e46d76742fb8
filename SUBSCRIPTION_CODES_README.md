# نظام أكواد الاشتراك - Quizimy

## 📋 نظرة عامة

تم تطوير نظام شامل لإنشاء وإدارة أكواد الاشتراك في تطبيق Quizimy. يسمح هذا النظام للأدمن بإنشاء أكواد اشتراك فريدة ومتابعة استخدامها.

## 🔐 صلاحيات الأدمن

**UID الأدمن المحدد:** `H4OwuI6NYYdhcaE8EOotikH4Fhf1`

فقط المستخدم بهذا UID يمكنه:
- الوصول لصفحة إنشاء أكواد الاشتراك
- إنشاء أكواد جديدة
- عرض جميع الأكواد المنشأة
- حذف الأكواد

## 🎯 المميزات

### ✅ إنشاء الأكواد
- **أكواد فريدة**: بتنسيق XXXX-XXXX
- **مدد مختلفة**: شهر، 3 أشهر، سنة كاملة
- **حفظ آمن**: في Firebase Firestore
- **تتبع شامل**: تاريخ الإنشاء والمنشئ

### ✅ إدارة الأكواد
- **عرض جميع الأكواد**: مع حالة كل كود
- **نسخ سريع**: لمشاركة الأكواد
- **حذف الأكواد**: غير المرغوب فيها
- **تتبع الاستخدام**: من استخدم الكود ومتى

### ✅ الأمان
- **تحقق من الصلاحيات**: في كل عملية
- **حماية قاعدة البيانات**: قواعد Firestore محكمة
- **تشفير البيانات**: حماية كاملة

## 📱 كيفية الاستخدام

### للأدمن:

1. **الدخول للإعدادات**
   - افتح التطبيق
   - اذهب لتبويب "الإعدادات"
   - ستجد خيار "إنشاء أكواد الاشتراك"

2. **إنشاء كود جديد**
   - اختر مدة الاشتراك (شهر/3 أشهر/سنة)
   - اضغط "إنشاء كود جديد"
   - سيظهر الكود الجديد في القائمة

3. **إدارة الأكواد**
   - انسخ الكود بالضغط على أيقونة النسخ
   - احذف الكود بالضغط على أيقونة الحذف
   - تابع حالة الكود (مستخدم/غير مستخدم)

### للمستخدمين العاديين:
- لن يظهر لهم خيار إنشاء الأكواد
- يمكنهم استخدام الأكواد عند توفر نظام الاستخدام

## 🗃️ هيكل البيانات

### مجموعة subscription_codes:
```json
{
  "code": "ABCD-1234",
  "durationMonths": 1,
  "createdAt": "timestamp",
  "createdBy": "H4OwuI6NYYdhcaE8EOotikH4Fhf1",
  "isUsed": false,
  "usedBy": null,
  "usedAt": null
}
```

## 🔧 الملفات المضافة

### النماذج:
- `lib/models/subscription_code_model.dart`

### الخدمات:
- `lib/services/subscription_service.dart`

### الشاشات:
- `lib/screens/create_subscription_codes_screen.dart`

### التحديثات:
- `lib/screens/settings_screen.dart` (إضافة خيار الأدمن)

### القواعد:
- `firestore.rules` (قواعد الأمان)

## 🚀 التشغيل

1. **تحديث قواعد Firestore**:
   ```bash
   firebase deploy --only firestore:rules
   ```

2. **تشغيل التطبيق**:
   ```bash
   flutter run
   ```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

1. **"غير مصرح لك بالوصول"**
   - تأكد من أن UID المستخدم هو الأدمن المحدد
   - تحقق من قواعد Firestore

2. **"لا توجد صلاحية للوصول لقاعدة البيانات"**
   - تحديث قواعد Firestore
   - التأكد من تسجيل الدخول

3. **الأكواد لا تظهر**
   - تحقق من اتصال الإنترنت
   - تأكد من إعدادات Firebase

## 📞 الدعم

في حالة وجود أي مشاكل أو استفسارات، يرجى التواصل مع فريق التطوير.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** ديسمبر 2024  
**الإصدار:** 1.0.0
