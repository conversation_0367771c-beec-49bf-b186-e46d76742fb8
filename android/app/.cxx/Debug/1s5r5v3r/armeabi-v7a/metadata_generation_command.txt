                        -H/Users/<USER>/development/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-D<PERSON><PERSON>OID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/26.3.11579264/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/quizimy/build/app/intermediates/cxx/Debug/1s5r5v3r/obj/armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Desktop/quizimy/build/app/intermediates/cxx/Debug/1s5r5v3r/obj/armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Desktop/quizimy/android/app/.cxx/Debug/1s5r5v3r/armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2