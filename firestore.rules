rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد للمستخدمين
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // قواعد لأكواد الاشتراك
    match /subscription_codes/{codeId} {
      // السماح للأدمن بالقراءة والكتابة والحذف
      allow read, write, delete: if request.auth != null && 
        request.auth.uid == "H4OwuI6NYYdhcaE8EOotikH4Fhf1";
      
      // السماح لجميع المستخدمين المسجلين بقراءة الأكواد لاستخدامها
      allow read: if request.auth != null;
      
      // السماح بتحديث حالة الكود عند الاستخدام
      allow update: if request.auth != null && 
        resource.data.isUsed == false &&
        request.resource.data.isUsed == true &&
        request.resource.data.usedBy == request.auth.uid;
    }
    
    // قواعد افتراضية - رفض الوصول لأي مجموعة أخرى
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
